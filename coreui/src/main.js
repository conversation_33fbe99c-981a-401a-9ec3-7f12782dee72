
import Vue from "vue";
import {CIcon} from './services/icons-vue/src'
// plugins
import vuetify from "./plugins/vuetify"; // path to vuetify export


import {iconsSet as icons} from "./assets/icons/icons.js";
import {storePromise} from "./store/";


// Import global CSS for frozen tables
import "./assets/css/frozen-tables.css";
// Import global CSS for sticky tables
import "./assets/css/sticky-tables.css";

import ECharts, {THEME_KEY} from "vue-echarts";

import "./plugins"


import App from "./App";
import router from "./router/index";

import filters from "./filters";
import middlewares from "./middlewares";
import mixin from "./mixins/index.js";



Vue.component("c-icon", CIcon);
Vue.component("e-charts", ECharts);

Vue.config.performance = true;

Object.keys(filters).forEach(filter => {
  Vue.filter(filter, filters[filter]);
});


Vue.mixin(mixin);

storePromise.then(async (store) => {
  // Make store globally accessible for components that need it
  window.$globalStore = store;

  await import("./plugins/axios");
  Object.keys(middlewares).forEach(name => {
    router.beforeEach(middlewares[name](store));
  });
  new Vue({
    el: "#app",
    store,
    router,
    provide: {
      [THEME_KEY]: 'white'
    },
    vuetify,
    async beforeCreate() {
      const websocketHost = import.meta.env.VITE_WEBSOCKET_HOST;
      const hostname = import.meta.env.MODE === 'production'
        ? window.location.hostname.split('.')[0]
        : import.meta.env.VITE_APP_NAME;
      await Promise.all([
        this.$store.dispatch("app/loadLocation"),
        this.$store.dispatch("initializeStore"),
        this.$store.dispatch(
          "app/loadOnlineChannel",
          `${websocketHost}/${hostname}/online`
        ),
      ]);
    },
    icons,
    template: "<App/>",
    components: {
      App
    }
  });
})

