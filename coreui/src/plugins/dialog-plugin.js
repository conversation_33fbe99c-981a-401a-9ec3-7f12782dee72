import GlobalDialog from '../components/common/MyDialog.vue';

const DialogPlugin = {
  install(Vue, options = {}) {
    // Create a constructor for the dialog component
    const DialogConstructor = Vue.extend(GlobalDialog);

    // Create a global instance
    let dialogInstance = null;

    // Create the $dialog method
    Vue.prototype.$dialog = {
      open(title, message, options = {}) {
        // Create instance if it doesn't exist
        if (!dialogInstance) {
          // Create the instance without store injection initially
          dialogInstance = new DialogConstructor();
          dialogInstance.$mount();
          document.body.appendChild(dialogInstance.$el);
        }

        // Call the open method
        return dialogInstance.open(title, message, options);
      }
    };

    // Also make it available as a global property
    Vue.prototype.$globalDialog = Vue.prototype.$dialog;
  }
};

export default DialogPlugin;